Đ<PERSON><PERSON> là tóm tắt ngắn gọn các thay đổi chính để tối ưu `filestore_client_v2.dart` nhằm hạn chế tràn RAM khi tải nhiều tệp lớn, tập trung vào việc kiểm soát bộ nhớ trên main isolate:

1.  **Thêm Global Semaphore (`_globalChunkMemorySemaphore`):**
    * **M<PERSON><PERSON> đích:** Giới hạn tổng số chunk dữ liệu (`Uint8List`) được phép tồn tại đồng thời trong bộ nhớ (chủ yếu là của main isolate) trên *tất cả* các tệp đang được tải lên.
    * **Khai báo:** Trong class `FilestoreClientV2`, khởi tạo với một giá trị giới hạn (ví dụ: `_maxGlobalConcurrentChunksInMemory = 5`).

2.  **Sửa đổi hàm `_sendChunkAsyncOptimized`:**
    * **`acquire()` semaphore:** G<PERSON>i `await _globalChunkMemorySemaphore.acquire();` ở đầu hàm, *trước khi* đọc dữ liệu chunk vào biến cục bộ `actualChunkData` bằng hàm `_readChunk`.
    * **`release()` semaphore:** Gọi `_globalChunkMemorySemaphore.release();` bên trong khối `finally` của hàm `_sendChunkAsyncOptimized`. Điều này đảm bảo semaphore luôn được giải phóng sau khi hàm hoàn tất xử lý chunk (dù thành công hay thất bại), giải phóng "slot" bộ nhớ cho chunk khác.
    * **Biến `actualChunkData`:** Sử dụng biến cục bộ `Uint8List? actualChunkData;` để chứa dữ liệu đọc từ `_readChunk` và gán `null` cho nó trong khối `finally` để hỗ trợ GC.

**Tác động của thay đổi:**

* Kiểm soát chặt chẽ lượng RAM đỉnh sử dụng bởi ứng dụng bằng cách ngăn không cho quá nhiều chunk dữ liệu lớn được nạp vào bộ nhớ cùng lúc.
* Giúp giảm đáng kể nguy cơ crash do tràn bộ nhớ trên iOS khi tải nhiều tệp lớn, ngay cả khi `AppConfig.chunkSize` không đổi.
* Cho phép mỗi tệp vẫn có thể cố gắng xử lý song song một số chunk (theo `maxConcurrentChunks` của tệp đó), nhưng sẽ bị điều tiết bởi giới hạn toàn cục của semaphore.